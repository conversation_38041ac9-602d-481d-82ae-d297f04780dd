import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

import {
  AssignRoleToEmployeeDto,
  CreateRoleDto,
  PermissionGroupsResponseDto,
  RoleDto,
  RoleQueryDto,
  UpdateRoleDto,
  UserPermissionsResponseDto,
  UserRolesResponseDto,
} from '../types/permission.types';

/**
 * Service cho các API liên quan đến phân quyền
 */
export const PermissionService = {
  /**
   * Lấy danh sách vai trò với phân trang và lọc
   * @param params Tham số truy vấn
   * @returns Danh sách vai trò đã phân trang
   */
  async getRoles(params?: RoleQueryDto): Promise<ApiResponseDto<PaginatedResult<RoleDto>>> {
    return apiClient.get<PaginatedResult<RoleDto>>('/api/hrm/roles', {
      params,
    });
  },

  /**
   * Lấy chi tiết vai trò theo ID
   * @param id ID vai trò
   * @returns Chi tiết vai trò
   */
  async getRole(id: number): Promise<ApiResponseDto<RoleDto>> {
    return apiClient.get<RoleDto>(`/api/hrm/roles/${id}`);
  },

  /**
   * Tạo mới vai trò
   * @param data Dữ liệu tạo vai trò
   * @returns Vai trò đã tạo
   */
  async createRole(data: CreateRoleDto): Promise<ApiResponseDto<RoleDto>> {
    return apiClient.post<RoleDto>('/api/hrm/roles', data);
  },

  /**
   * Cập nhật vai trò
   * @param id ID vai trò
   * @param data Dữ liệu cập nhật
   * @returns Vai trò đã cập nhật
   */
  async updateRole(id: number, data: UpdateRoleDto): Promise<ApiResponseDto<RoleDto>> {
    return apiClient.put<RoleDto>(`/api/hrm/roles/${id}`, data);
  },

  /**
   * Xóa vai trò
   * @param id ID vai trò
   * @returns Kết quả xóa
   */
  async deleteRole(id: number): Promise<ApiResponseDto<boolean>> {
    return apiClient.delete<boolean>(`/api/hrm/roles/${id}`);
  },

  /**
   * Lấy danh sách quyền theo nhóm
   * @returns Danh sách quyền theo nhóm
   */
  async getPermissionGroups(): Promise<ApiResponseDto<PermissionGroupsResponseDto>> {
    return apiClient.get<PermissionGroupsResponseDto>('/api/hrm/employees/permissions/groups');
  },

  /**
   * Lấy danh sách vai trò của nhân viên
   * @param employeeId ID nhân viên
   * @returns Danh sách vai trò của nhân viên
   */
  async getEmployeeRoles(employeeId: number): Promise<ApiResponseDto<UserRolesResponseDto>> {
    return apiClient.get<UserRolesResponseDto>(`/api/hrm/employees/${employeeId}/roles`);
  },

  /**
   * Gán vai trò cho nhân viên
   * @param employeeId ID nhân viên
   * @param data Dữ liệu gán vai trò
   * @returns Kết quả gán vai trò
   */
  async assignRoleToEmployee(
    employeeId: number,
    data: AssignRoleToEmployeeDto
  ): Promise<ApiResponseDto<any>> {
    return apiClient.post<any>(`/api/hrm/employees/${employeeId}/roles`, data);
  },

  /**
   * Lấy danh sách quyền của người dùng
   * @param userId ID người dùng
   * @returns Danh sách quyền của người dùng
   */
  async getUserPermissions(userId: number): Promise<ApiResponseDto<UserPermissionsResponseDto>> {
    return apiClient.get<UserPermissionsResponseDto>(
      `/api/hrm/employees/permissions/user/${userId}`
    );
  },

  /**
   * Cập nhật quyền trực tiếp cho nhân viên
   * @param employeeId ID nhân viên
   * @param data Dữ liệu cập nhật quyền
   * @returns Kết quả cập nhật quyền
   */
  async updateEmployeePermission(
    employeeId: number,
    data: { permissionIds: number[] }
  ): Promise<ApiResponseDto<any>> {
    return apiClient.post<any>('/api/hrm/employees/permissions/update-employee-permission', {
      employeeId,
      permissionIds: data.permissionIds,
    });
  },
};
