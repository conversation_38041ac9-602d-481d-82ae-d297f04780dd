import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Card, Checkbox, Typography } from '@/shared/components/common';

import { useEmployee } from '../../hooks/useEmployees';
import { useAssignRoleToEmployee, useEmployeeRoles, useRoles } from '../../hooks/usePermissions';
import { RoleDto } from '../../types/permission.types';

interface EmployeePermissionProps {
  employeeId: number;
  onClose?: () => void;
}

/**
 * Component phân quyền cho nhân viên
 */
const EmployeePermission: React.FC<EmployeePermissionProps> = ({ employeeId, onClose }) => {
  const { t } = useTranslation(['hrm', 'common']);
  const [selectedRoleIds, setSelectedRoleIds] = useState<number[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Lấy thông tin nhân viên
  const { data: employeeData, isLoading: isLoadingEmployee } = useEmployee(employeeId);

  // Lấy danh sách vai trò
  const { data: rolesData, isLoading: isLoadingRoles } = useRoles();

  // Lấy danh sách vai trò của nhân viên
  const { data: employeeRolesData, isLoading: isLoadingEmployeeRoles } =
    useEmployeeRoles(employeeId);

  // Mutation để gán vai trò cho nhân viên
  const assignRoleMutation = useAssignRoleToEmployee();

  // Cập nhật selectedRoleIds khi có dữ liệu vai trò của nhân viên
  useEffect(() => {
    if (employeeRolesData && employeeRolesData.roles) {
      setSelectedRoleIds(employeeRolesData.roles.map(role => role.id));
    }
  }, [employeeRolesData]);

  // Xử lý khi chọn/bỏ chọn vai trò
  const handleRoleChange = (roleId: number, checked: boolean) => {
    console.log('Role change:', roleId, checked);
    if (checked) {
      setSelectedRoleIds(prev => [...prev, roleId]);
    } else {
      setSelectedRoleIds(prev => prev.filter(id => id !== roleId));
    }
  };

  // Xử lý khi lưu phân quyền
  const handleSave = async () => {
    if (!employeeId) {
      return;
    }

    setIsSubmitting(true);
    try {
      await assignRoleMutation.mutateAsync({
        employeeId,
        data: { roleIds: selectedRoleIds },
      });

      if (onClose) {
        onClose();
      }
    } catch (error) {
      console.error('Error assigning roles:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const isLoading = isLoadingRoles || isLoadingEmployeeRoles || isLoadingEmployee;

  return (
    <div className="space-y-4">
      <div className="mb-6">
        <Typography variant="h4" className="mb-4 text-foreground">
          {t('hrm:employee.permission.title', 'Phân quyền vai trò')}
        </Typography>

        {/* Thông tin nhân viên */}
        {employeeData && (
          <Card className="p-4 mb-4 bg-muted/50">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                <Typography variant="h6" className="text-primary font-semibold">
                  {employeeData.employeeName?.charAt(0)?.toUpperCase() || 'N'}
                </Typography>
              </div>
              <div className="flex-1">
                <Typography variant="h6" className="text-foreground font-semibold">
                  {employeeData.employeeName || t('hrm:employee.noName', 'Chưa có tên')}
                </Typography>
                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  <span>
                    {t('hrm:employee.code', 'Mã NV')}: {employeeData.employeeCode}
                  </span>
                  {employeeData.jobTitle && (
                    <span>
                      {t('hrm:employee.jobTitle', 'Chức vụ')}: {employeeData.jobTitle}
                    </span>
                  )}
                  {employeeData.departmentId && (
                    <span>
                      {t('hrm:employee.department', 'Phòng ban')}: ID {employeeData.departmentId}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </Card>
        )}
      </div>

      {isLoading ? (
        <div className="py-4 text-center">{t('common:loading', 'Đang tải...')}</div>
      ) : (
        <Card className="p-4">
          <div className="space-y-3">
            {rolesData?.items &&
              rolesData.items.map((role: RoleDto) => (
                <div
                  key={role.id}
                  className="flex items-center space-x-3 p-3 hover:bg-muted/50 rounded-md transition-colors border border-border"
                >
                  <Checkbox
                    id={`role-${role.id}`}
                    checked={selectedRoleIds.includes(role.id)}
                    onChange={checked => handleRoleChange(role.id, checked)}
                  />
                  <div className="flex-1">
                    <label
                      htmlFor={`role-${role.id}`}
                      className="font-medium text-base cursor-pointer text-foreground"
                    >
                      {role.name}
                    </label>
                    <div className="mt-1 text-xs text-muted-foreground">
                      {t('hrm:permission.permissionCount', '{{count}} quyền', {
                        count: role.permissions?.length || 0,
                      })}
                    </div>
                  </div>
                </div>
              ))}

            {(!rolesData?.items || rolesData.items.length === 0) && (
              <div className="py-4 text-center text-muted-foreground">
                {t('hrm:permission.noRoles', 'Không có vai trò nào')}
              </div>
            )}
          </div>
        </Card>
      )}

      <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-border">
        {onClose && (
          <Button variant="outline" onClick={onClose}>
            {t('common:cancel', 'Hủy')}
          </Button>
        )}
        <Button
          variant="primary"
          onClick={handleSave}
          disabled={isSubmitting}
          isLoading={isSubmitting}
        >
          {isSubmitting ? t('common:saving', 'Đang lưu...') : t('common:save', 'Lưu')}
        </Button>
      </div>
    </div>
  );
};

export default EmployeePermission;
