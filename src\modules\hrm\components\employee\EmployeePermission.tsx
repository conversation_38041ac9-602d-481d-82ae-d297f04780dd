import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Card, Checkbox, Typography } from '@/shared/components/common';

import { useAssignRoleToEmployee, useEmployeeRoles, useRoles } from '../../hooks/usePermissions';
import { RoleDto } from '../../types/permission.types';

interface EmployeePermissionProps {
  employeeId: number;
  onClose?: () => void;
}

/**
 * Component phân quyền cho nhân viên
 */
const EmployeePermission: React.FC<EmployeePermissionProps> = ({ employeeId, onClose }) => {
  const { t } = useTranslation(['hrm', 'common']);
  const [selectedRoleIds, setSelectedRoleIds] = useState<number[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // L<PERSON>y danh sách vai trò
  const { data: rolesData, isLoading: isLoadingRoles } = useRoles();

  // Lấy danh sách vai trò của nhân viên
  const { data: employeeRolesData, isLoading: isLoadingEmployeeRoles } =
    useEmployeeRoles(employeeId);

  // Mutation để gán vai trò cho nhân viên
  const assignRoleMutation = useAssignRoleToEmployee();

  // Cập nhật selectedRoleIds khi có dữ liệu vai trò của nhân viên
  useEffect(() => {
    if (employeeRolesData && employeeRolesData.roles) {
      setSelectedRoleIds(employeeRolesData.roles.map(role => role.id));
    }
  }, [employeeRolesData]);

  // Xử lý khi chọn/bỏ chọn vai trò
  const handleRoleChange = (roleId: number, checked: boolean) => {
    console.log('Role change:', roleId, checked);
    if (checked) {
      setSelectedRoleIds(prev => [...prev, roleId]);
    } else {
      setSelectedRoleIds(prev => prev.filter(id => id !== roleId));
    }
  };

  // Xử lý khi lưu phân quyền
  const handleSave = async () => {
    if (!employeeId) {
      return;
    }

    setIsSubmitting(true);
    try {
      await assignRoleMutation.mutateAsync({
        employeeId,
        data: { roleIds: selectedRoleIds },
      });

      if (onClose) {
        onClose();
      }
    } catch (error) {
      console.error('Error assigning roles:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const isLoading = isLoadingRoles || isLoadingEmployeeRoles;

  return (
    <div className="space-y-4">
      <div className="mb-4">
        <Typography variant="h4" className="mb-2 text-white dark:text-white">
          {t('hrm:employee.permission.title', 'Phân quyền cho nhân viên')}
        </Typography>
        <Typography variant="body2" className="text-white dark:text-white opacity-80">
          {t('hrm:employee.permission.description', 'Chọn các vai trò để gán cho nhân viên này')}
        </Typography>
      </div>

      {isLoading ? (
        <div className="py-4 text-center">{t('common:loading', 'Đang tải...')}</div>
      ) : (
        <Card className="p-4 bg-primary-900/30 dark:bg-primary-900/30">
          <div className="space-y-2">
            {rolesData?.items &&
              rolesData.items.map((role: RoleDto) => (
                <div
                  key={role.id}
                  className="flex items-start space-x-3 p-2 hover:bg-primary-800/50 dark:hover:bg-primary-800/50 rounded-md transition-colors"
                >
                  <Checkbox
                    id={`role-${role.id}`}
                    checked={selectedRoleIds.includes(role.id)}
                    onChange={checked => handleRoleChange(role.id, checked)}
                    variant="filled"
                  />
                  <div>
                    <label
                      htmlFor={`role-${role.id}`}
                      className="font-medium text-base cursor-pointer text-white dark:text-white"
                    >
                      {role.name}
                    </label>
                    {role.description && (
                      <p className="text-sm text-white dark:text-white opacity-90 mt-1">
                        {role.description}
                      </p>
                    )}
                    <div className="mt-1 text-xs text-white dark:text-white opacity-80">
                      {t('hrm:permission.permissionCount', '{{count}} quyền', {
                        count: role.permissions?.length || 0,
                      })}
                    </div>
                  </div>
                </div>
              ))}

            {(!rolesData?.items || rolesData.items.length === 0) && (
              <div className="py-4 text-center text-white dark:text-white">
                {t('hrm:permission.noRoles', 'Không có vai trò nào')}
              </div>
            )}
          </div>
        </Card>
      )}

      <div className="flex justify-end space-x-2 mt-4">
        {onClose && (
          <Button variant="outline" onClick={onClose} className="text-white dark:text-white">
            {t('common:cancel', 'Hủy')}
          </Button>
        )}
        <Button onClick={handleSave} disabled={isSubmitting} className="bg-primary text-white">
          {isSubmitting ? t('common:saving', 'Đang lưu...') : t('common:save', 'Lưu')}
        </Button>
      </div>
    </div>
  );
};

export default EmployeePermission;
